#!/usr/bin/env python3
"""
测试累积式折叠函数
"""

import numpy as np
from DLlib.data.folds import cumulative_splits_by_days, get_cumulative_fold_info


def test_cumulative_splits():
    """测试累积式切分功能"""
    print("=== 测试累积式切分功能 ===")
    
    # 测试参数
    D = 100  # 总天数
    train_days = 30  # 每折训练天数
    inner_val_ratio = 0.2  # 验证集比例
    
    print(f"总天数: {D}")
    print(f"每折训练天数: {train_days}")
    print(f"验证集比例: {inner_val_ratio}")
    print()
    
    # 生成折叠
    folds = list(cumulative_splits_by_days(
        D, 
        train_days=train_days, 
        inner_val_ratio=inner_val_ratio
    ))
    
    print(f"生成了 {len(folds)} 个折叠:")
    print()
    
    all_train_days = set()
    
    for i, (idx_train, idx_valid, idx_test) in enumerate(folds):
        print(f"Fold {i}:")
        print(f"  训练集: {len(idx_train)} 天 - {list(idx_train[:5])}{'...' if len(idx_train) > 5 else ''}")
        print(f"  验证集: {len(idx_valid)} 天 - {list(idx_valid) if len(idx_valid) <= 10 else list(idx_valid[:5]) + ['...']}")
        print(f"  测试集: {len(idx_test)} 天 - {list(idx_test[:5])}{'...' if len(idx_test) > 5 else ''}")
        
        # 验证没有重叠
        train_set = set(idx_train)
        valid_set = set(idx_valid)
        test_set = set(idx_test)
        
        # 检查训练集和验证集没有重叠
        assert len(train_set & valid_set) == 0, f"Fold {i}: 训练集和验证集有重叠"
        
        # 检查训练集和测试集没有重叠
        assert len(train_set & test_set) == 0, f"Fold {i}: 训练集和测试集有重叠"
        
        # 检查验证集和测试集没有重叠
        assert len(valid_set & test_set) == 0, f"Fold {i}: 验证集和测试集有重叠"
        
        # 检查所有集合加起来覆盖全部天数
        all_days = train_set | valid_set | test_set
        assert all_days == set(range(D)), f"Fold {i}: 未覆盖全部天数"
        
        # 累积训练天数
        all_train_days.update(train_set)
        
        print(f"  累积训练天数: {len(all_train_days)}")
        print()
    
    # 验证最终训练集覆盖全部天数
    assert all_train_days == set(range(D)), "最终训练集未覆盖全部天数"
    print("✓ 所有验证通过!")
    print()
    
    # 获取统计信息
    info = get_cumulative_fold_info(D, train_days, inner_val_ratio)
    print("=== 统计信息 ===")
    print(f"折叠数量: {info['num_folds']}")
    print(f"最终训练覆盖率: {info['final_train_coverage']:.2%}")
    print(f"每窗口训练天数: {info['train_days_per_window']}")
    print(f"验证集比例: {info['inner_val_ratio']}")
    print()
    
    print("各折详细信息:")
    for stat in info['fold_stats']:
        print(f"  Fold {stat['fold']}: 训练={stat['train_days']}, 验证={stat['valid_days']}, 测试={stat['test_days']}")


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试小数据集
    print("测试小数据集 (D=10, train_days=5):")
    folds = list(cumulative_splits_by_days(10, train_days=5, inner_val_ratio=0.2))
    print(f"生成 {len(folds)} 个折叠")
    
    for i, (train, valid, test) in enumerate(folds):
        print(f"  Fold {i}: train={len(train)}, valid={len(valid)}, test={len(test)}")
    
    # 测试训练天数大于总天数
    print("\n测试训练天数大于总天数 (D=10, train_days=15):")
    folds = list(cumulative_splits_by_days(10, train_days=15, inner_val_ratio=0.2))
    print(f"生成 {len(folds)} 个折叠")
    
    for i, (train, valid, test) in enumerate(folds):
        print(f"  Fold {i}: train={len(train)}, valid={len(valid)}, test={len(test)}")


if __name__ == "__main__":
    test_cumulative_splits()
    test_edge_cases()
