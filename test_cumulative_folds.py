#!/usr/bin/env python3
"""
测试累积式折叠函数
"""

import numpy as np
from DLlib.data.folds import cumulative_splits_by_days, get_cumulative_fold_info


def test_cumulative_splits():
    """测试连续分段式切分功能"""
    print("=== 测试连续分段式切分功能 ===")

    # 测试参数
    D = 100  # 总天数
    train_days = 30  # 每折训练天数
    inner_val_ratio = 0.2  # 验证集比例

    print(f"总天数: {D}")
    print(f"每折训练天数: {train_days}")
    print(f"验证集比例: {inner_val_ratio}")
    print()

    # 生成折叠
    folds = list(cumulative_splits_by_days(
        D,
        train_days=train_days,
        inner_val_ratio=inner_val_ratio
    ))

    print(f"生成了 {len(folds)} 个折叠:")
    print()

    all_train_days = set()
    all_train_windows = set()  # 包括验证集的完整训练窗口

    for i, (idx_train, idx_valid, idx_test) in enumerate(folds):
        # 当前折的完整训练窗口（训练集+验证集）
        train_window = set(idx_train) | set(idx_valid)

        print(f"Fold {i}:")
        print(f"  训练集: {len(idx_train)} 天 - 范围 [{min(idx_train) if len(idx_train) > 0 else 'N/A'}:{max(idx_train)+1 if len(idx_train) > 0 else 'N/A'}]")
        print(f"  验证集: {len(idx_valid)} 天 - 范围 [{min(idx_valid) if len(idx_valid) > 0 else 'N/A'}:{max(idx_valid)+1 if len(idx_valid) > 0 else 'N/A'}]")
        print(f"  测试集: {len(idx_test)} 天")

        # 验证没有重叠
        train_set = set(idx_train)
        valid_set = set(idx_valid)
        test_set = set(idx_test)

        # 检查训练集和验证集没有重叠
        assert len(train_set & valid_set) == 0, f"Fold {i}: 训练集和验证集有重叠"

        # 检查训练窗口和测试集没有重叠
        assert len(train_window & test_set) == 0, f"Fold {i}: 训练窗口和测试集有重叠"

        # 检查所有集合加起来覆盖全部天数
        all_days = train_set | valid_set | test_set
        assert all_days == set(range(D)), f"Fold {i}: 未覆盖全部天数"

        # 检查训练窗口是连续的
        if len(train_window) > 0:
            min_day = min(train_window)
            max_day = max(train_window)
            expected_window = set(range(min_day, max_day + 1))
            assert train_window == expected_window, f"Fold {i}: 训练窗口不连续"

        # 累积训练天数和训练窗口
        all_train_days.update(train_set)
        all_train_windows.update(train_window)

        print(f"  训练窗口范围: [{min(train_window) if train_window else 'N/A'}:{max(train_window)+1 if train_window else 'N/A'}]")
        print(f"  累积训练天数: {len(all_train_days)}")
        print()

    # 验证所有训练窗口覆盖全部天数
    assert all_train_windows == set(range(D)), "所有训练窗口未覆盖全部天数"
    print("✓ 所有验证通过!")
    print()
    
    # 获取统计信息
    info = get_cumulative_fold_info(D, train_days, inner_val_ratio)
    print("=== 统计信息 ===")
    print(f"折叠数量: {info['num_folds']}")
    print(f"最终训练覆盖率: {info['final_train_coverage']:.2%}")
    print(f"每窗口训练天数: {info['train_days_per_window']}")
    print(f"验证集比例: {info['inner_val_ratio']}")
    print()
    
    print("各折详细信息:")
    for stat in info['fold_stats']:
        print(f"  Fold {stat['fold']}: 训练={stat['train_days']}, 验证={stat['valid_days']}, 测试={stat['test_days']}")


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试小数据集
    print("测试小数据集 (D=10, train_days=5):")
    folds = list(cumulative_splits_by_days(10, train_days=5, inner_val_ratio=0.2))
    print(f"生成 {len(folds)} 个折叠")
    
    for i, (train, valid, test) in enumerate(folds):
        print(f"  Fold {i}: train={len(train)}, valid={len(valid)}, test={len(test)}")
    
    # 测试训练天数大于总天数
    print("\n测试训练天数大于总天数 (D=10, train_days=15):")
    folds = list(cumulative_splits_by_days(10, train_days=15, inner_val_ratio=0.2))
    print(f"生成 {len(folds)} 个折叠")
    
    for i, (train, valid, test) in enumerate(folds):
        print(f"  Fold {i}: train={len(train)}, valid={len(valid)}, test={len(test)}")


if __name__ == "__main__":
    test_cumulative_splits()
    test_edge_cases()
