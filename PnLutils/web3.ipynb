{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e5c108db", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[[[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[ 0.04026465],\n", "         [ 0.04321196],\n", "         [ 0.04496395],\n", "         ...,\n", "         [-0.05173629],\n", "         [-0.0498496 ],\n", "         [-0.04560285]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        ...,\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]]],\n", "\n", "\n", "       [[[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[-0.02999   ],\n", "         [-0.05693191],\n", "         [-0.03812536],\n", "         ...,\n", "         [-0.03358203],\n", "         [-0.03470346],\n", "         [-0.03683208]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        ...,\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]]],\n", "\n", "\n", "       [[[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[-0.06971893],\n", "         [-0.01837485],\n", "         [-0.01804087],\n", "         ...,\n", "         [-0.04209317],\n", "         [-0.03836344],\n", "         [-0.04364147]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        ...,\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]],\n", "\n", "        [[        nan],\n", "         [        nan],\n", "         [        nan],\n", "         ...,\n", "         [        nan],\n", "         [        nan],\n", "         [        nan]]],\n", "\n", "\n", "       ...,\n", "\n", "\n", "       [[[ 0.03664007],\n", "         [ 0.05709678],\n", "         [ 0.04866231],\n", "         ...,\n", "         [ 0.04013343],\n", "         [ 0.03997838],\n", "         [ 0.04071369]],\n", "\n", "        [[-0.00757273],\n", "         [-0.02776575],\n", "         [-0.02902272],\n", "         ...,\n", "         [ 0.04162486],\n", "         [ 0.04281908],\n", "         [ 0.04580533]],\n", "\n", "        [[ 0.0087676 ],\n", "         [ 0.03757894],\n", "         [ 0.04526326],\n", "         ...,\n", "         [ 0.03605487],\n", "         [ 0.03355226],\n", "         [ 0.02625228]],\n", "\n", "        ...,\n", "\n", "        [[-0.00815746],\n", "         [-0.02208492],\n", "         [-0.02095466],\n", "         ...,\n", "         [ 0.04029237],\n", "         [ 0.03794557],\n", "         [ 0.03579073]],\n", "\n", "        [[ 0.01346222],\n", "         [-0.03920129],\n", "         [-0.03292948],\n", "         ...,\n", "         [-0.03719192],\n", "         [-0.03569761],\n", "         [-0.03225395]],\n", "\n", "        [[ 0.04204086],\n", "         [ 0.0413003 ],\n", "         [ 0.04002298],\n", "         ...,\n", "         [ 0.04433157],\n", "         [ 0.04372433],\n", "         [ 0.04380967]]],\n", "\n", "\n", "       [[[ 0.04289036],\n", "         [ 0.0467431 ],\n", "         [ 0.04542415],\n", "         ...,\n", "         [ 0.04721512],\n", "         [ 0.04420956],\n", "         [ 0.04145052]],\n", "\n", "        [[-0.01163497],\n", "         [-0.01920947],\n", "         [-0.00615036],\n", "         ...,\n", "         [ 0.04410032],\n", "         [ 0.04716982],\n", "         [ 0.04341954]],\n", "\n", "        [[-0.02684745],\n", "         [-0.02380594],\n", "         [-0.03042963],\n", "         ...,\n", "         [ 0.03845951],\n", "         [ 0.03893799],\n", "         [ 0.03320031]],\n", "\n", "        ...,\n", "\n", "        [[-0.03362082],\n", "         [-0.04195723],\n", "         [-0.00224184],\n", "         ...,\n", "         [ 0.03965783],\n", "         [ 0.04002169],\n", "         [ 0.03844521]],\n", "\n", "        [[ 0.00104392],\n", "         [ 0.00510684],\n", "         [ 0.01803119],\n", "         ...,\n", "         [-0.03858736],\n", "         [-0.03866705],\n", "         [-0.03826341]],\n", "\n", "        [[-0.0112411 ],\n", "         [ 0.02563418],\n", "         [ 0.03137076],\n", "         ...,\n", "         [ 0.0375202 ],\n", "         [ 0.03472206],\n", "         [ 0.02850375]]],\n", "\n", "\n", "       [[[ 0.01721001],\n", "         [ 0.04366537],\n", "         [ 0.04406379],\n", "         ...,\n", "         [-0.03187643],\n", "         [-0.03187639],\n", "         [-0.03187642]],\n", "\n", "        [[-0.01525416],\n", "         [-0.01483693],\n", "         [-0.01820757],\n", "         ...,\n", "         [-0.0368509 ],\n", "         [-0.03685101],\n", "         [-0.0368509 ]],\n", "\n", "        [[ 0.0111706 ],\n", "         [ 0.03938494],\n", "         [ 0.04506746],\n", "         ...,\n", "         [-0.0485622 ],\n", "         [-0.04856133],\n", "         [-0.04856068]],\n", "\n", "        ...,\n", "\n", "        [[ 0.02722803],\n", "         [ 0.03797865],\n", "         [ 0.04123134],\n", "         ...,\n", "         [ 0.04249353],\n", "         [ 0.04249332],\n", "         [ 0.04249362]],\n", "\n", "        [[-0.00844669],\n", "         [-0.02277189],\n", "         [-0.01475244],\n", "         ...,\n", "         [ 0.03970455],\n", "         [ 0.0397047 ],\n", "         [ 0.03970464]],\n", "\n", "        [[ 0.01991276],\n", "         [ 0.03112707],\n", "         [ 0.03498461],\n", "         ...,\n", "         [ 0.04322029],\n", "         [ 0.0432202 ],\n", "         [ 0.04322008]]]], shape=(1461, 355, 96, 1), dtype=float32)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "y = np.load(\"/home/<USER>/tslib/projs/web3/checkpoints/daysection08141110_GRU_SAttn_304/day_fold00/test_preds.npy\")\n", "\n", "y"]}, {"cell_type": "code", "execution_count": null, "id": "5df4be65", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lyc", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}