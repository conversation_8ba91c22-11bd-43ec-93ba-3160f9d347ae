"""
滚动切分生成器

提供按天或按年的滚动训练/验证/测试切分功能。
"""

from __future__ import annotations
from typing import Iterator, Tuple, Optional
import numpy as np


def rolling_splits_by_days(
    D: int,
    *,
    train_days: int,
    test_days: int,
    inner_val_ratio: float = 0.2,    # 训练窗口内最后 20% 做 valid
    step_days: Optional[int] = None, # 每次滚动步长（默认= test_days）
    drop_last_incomplete: bool = True,
) -> Iterator[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按天生成滚动切分
    
    参数:
        D: 总天数
        train_days: 训练窗口天数
        test_days: 测试窗口天数
        inner_val_ratio: 训练窗口内验证集比例
        step_days: 滚动步长，默认等于test_days
        drop_last_incomplete: 是否丢弃最后不完整的fold
    
    生成:
        (idx_train, idx_valid, idx_test): 训练、验证、测试索引
        
    示例:
        D=100, train_days=60, test_days=20, inner_val_ratio=0.2
        - Fold 0: train=[0:48], valid=[48:60], test=[60:80]
        - Fold 1: train=[20:68], valid=[68:80], test=[80:100]
    """
    if step_days is None:
        step_days = test_days
        
    start = 0
    while True:
        tr_beg = start
        tr_end = tr_beg + train_days
        te_end = tr_end + test_days
        
        # 检查训练窗口是否超出范围
        if tr_end > D:
            break
            
        # 检查测试窗口是否超出范围
        if te_end > D:
            if drop_last_incomplete:
                break
            te_end = D
            
        # 构造训练和验证索引
        idx_train_full = np.arange(tr_beg, tr_end, dtype=np.int64)
        n_tr = len(idx_train_full)
        n_val = max(1, int(round(n_tr * inner_val_ratio)))
        n_fit = n_tr - n_val
        
        idx_train = idx_train_full[:n_fit]
        idx_valid = idx_train_full[n_fit:]
        
        # 构造测试索引
        idx_test = np.arange(tr_end, te_end, dtype=np.int64)
        
        if len(idx_test) == 0:
            break
            
        yield idx_train, idx_valid, idx_test
        
        start += step_days


def rolling_splits_by_years(
    D: int,
    *,
    train_years: float,
    test_years: float,
    days_per_year: int = 252,  # 交易日
    inner_val_ratio: float = 0.2,
    step_years: Optional[float] = None,
    drop_last_incomplete: bool = True,
) -> Iterator[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按年生成滚动切分
    
    参数:
        D: 总天数
        train_years: 训练窗口年数
        test_years: 测试窗口年数
        days_per_year: 每年交易日数
        inner_val_ratio: 训练窗口内验证集比例
        step_years: 滚动步长年数，默认等于test_years
        drop_last_incomplete: 是否丢弃最后不完整的fold
    
    生成:
        (idx_train, idx_valid, idx_test): 训练、验证、测试索引
        
    示例:
        train_years=3, test_years=1, days_per_year=252
        相当于 train_days=756, test_days=252
    """
    if step_years is None:
        step_years = test_years
        
    return rolling_splits_by_days(
        D,
        train_days=int(round(train_years * days_per_year)),
        test_days=int(round(test_years * days_per_year)),
        step_days=int(round(step_years * days_per_year)),
        inner_val_ratio=inner_val_ratio,
        drop_last_incomplete=drop_last_incomplete,
    )


def rolling_global_splits_by_days(
    D: int,
    *,
    train_days: int,
    inner_val_ratio: float = 0.2,    # 训练窗口内验证集比例，默认1:4
    step_days: Optional[int] = None, # 每次滚动步长（默认= train_days）
    drop_last_incomplete: bool = True,
) -> Iterator[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    按天生成滚动切分，测试集为全局数据

    参数:
        D: 总天数
        train_days: 训练窗口天数
        inner_val_ratio: 训练窗口内验证集比例
        step_days: 滚动步长，默认等于train_days
        drop_last_incomplete: 是否丢弃最后不完整的fold

    生成:
        (idx_train, idx_valid, idx_test): 训练、验证、测试索引

    示例:
        D=1462, train_days=180, inner_val_ratio=0.2, step_days=180
        - Fold 0: train=[0:144], valid=[144:180], test=[0:1462]
        - Fold 1: train=[180:324], valid=[324:360], test=[0:1462]
        - Fold 2: train=[360:504], valid=[504:540], test=[0:1462]
        ...

    注意:
        - 测试集始终是全部D天的数据
        - 每个fold的训练窗口按inner_val_ratio划分为训练集和验证集
        - 滚动步长控制训练窗口的移动
    """
    if step_days is None:
        step_days = train_days

    # 全局测试集索引（所有天数）
    idx_test_global = np.arange(0, D, dtype=np.int64)

    start = 0
    while True:
        tr_beg = start
        tr_end = tr_beg + train_days

        # 检查训练窗口是否超出范围
        if tr_end > D:
            if drop_last_incomplete:
                break
            tr_end = D

        # 构造训练和验证索引
        idx_train_full = np.arange(tr_beg, tr_end, dtype=np.int64)
        n_tr = len(idx_train_full)

        if n_tr == 0:
            break

        n_val = max(1, int(round(n_tr * inner_val_ratio)))
        n_fit = n_tr - n_val

        idx_train = idx_train_full[:n_fit]
        idx_valid = idx_train_full[n_fit:]

        yield idx_train, idx_valid, idx_test_global

        start += step_days


def get_fold_info(
    D: int,
    train_years: float,
    test_years: float,
    days_per_year: int = 252,
    **kwargs
) -> dict:
    """
    获取fold信息统计

    返回:
        包含fold数量、覆盖范围等信息的字典
    """
    folds = list(rolling_splits_by_years(
        D,
        train_years=train_years,
        test_years=test_years,
        days_per_year=days_per_year,
        **kwargs
    ))

    if not folds:
        return {"num_folds": 0, "coverage": 0.0}

    # 计算测试集覆盖的天数
    test_days_covered = set()
    for _, _, idx_test in folds:
        test_days_covered.update(idx_test)

    return {
        "num_folds": len(folds),
        "coverage": len(test_days_covered) / D,
        "train_days_per_fold": len(folds[0][0]) + len(folds[0][1]),
        "test_days_per_fold": len(folds[0][2]),
        "total_test_days": len(test_days_covered),
    }


def cumulative_splits_by_days(
    D: int,
    *,
    train_days: int,
    inner_val_ratio: float = 0.2,    # 训练窗口内验证集比例
) -> Iterator[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
    """
    连续分段式切分，每个折的验证集和测试集都是除了训练集之外的所有天

    参数:
        D: 总天数
        train_days: 每折的训练窗口天数
        inner_val_ratio: 训练窗口内验证集比例

    生成:
        (idx_train, idx_valid, idx_test): 训练、验证、测试索引

    示例:
        D=100, train_days=30, inner_val_ratio=0.2
        - Fold 0: train=[0:24], valid=[24:30], test=[30:100]
        - Fold 1: train=[30:54], valid=[54:60], test=[0:30, 60:100]
        - Fold 2: train=[60:84], valid=[84:90], test=[0:60, 90:100]
        - Fold 3: train=[90:100], valid=[], test=[0:90] (最后一折，剩余全部作为训练集)

    特点:
        - 每个折的训练集是连续的天数段，所有折的训练集加起来覆盖全部天数
        - 验证集和测试集是除了当前训练集之外的所有天
        - 最后一折如果训练集天数不足train_days，会把剩余全部天数作为训练集
    """
    if D <= 0 or train_days <= 0:
        return

    start = 0

    while start < D:
        # 计算当前折的训练窗口结束位置
        tr_end = min(start + train_days, D)

        # 如果这是最后一段且长度不足train_days，扩展到末尾
        if tr_end < D and (D - start) < train_days:
            tr_end = D

        # 当前折的训练天数（连续段）
        train_days_current = list(range(start, tr_end))

        # 构造训练和验证索引
        idx_train_full = np.array(train_days_current, dtype=np.int64)
        n_tr = len(idx_train_full)

        if n_tr == 0:
            break

        # 计算验证集大小（基于当前训练窗口）
        n_val = max(0, int(round(n_tr * inner_val_ratio)))
        n_fit = n_tr - n_val

        idx_train = idx_train_full[:n_fit]
        idx_valid = idx_train_full[n_fit:] if n_val > 0 else np.array([], dtype=np.int64)

        # 测试集是除了当前训练窗口之外的所有天
        all_days = set(range(D))
        train_window_set = set(idx_train_full)  # 整个训练窗口（包括验证集）
        test_days_set = all_days - train_window_set
        idx_test = np.array(sorted(test_days_set), dtype=np.int64)

        yield idx_train, idx_valid, idx_test

        # 移动到下一个窗口
        start = tr_end

        # 如果已经到达末尾，结束
        if start >= D:
            break


def get_global_fold_info(
    D: int,
    train_days: int,
    inner_val_ratio: float = 0.2,
    step_days: Optional[int] = None,
    **kwargs
) -> dict:
    """
    获取全局测试集fold信息统计

    返回:
        包含fold数量、训练窗口信息等的字典
    """
    folds = list(rolling_global_splits_by_days(
        D,
        train_days=train_days,
        inner_val_ratio=inner_val_ratio,
        step_days=step_days,
        **kwargs
    ))

    if not folds:
        return {"num_folds": 0}

    return {
        "num_folds": len(folds),
        "train_days_per_fold": len(folds[0][0]),
        "valid_days_per_fold": len(folds[0][1]),
        "test_days_per_fold": len(folds[0][2]),  # 应该等于D
        "total_train_window": len(folds[0][0]) + len(folds[0][1]),
        "step_days": step_days or train_days,
    }


def get_cumulative_fold_info(
    D: int,
    train_days: int,
    inner_val_ratio: float = 0.2,
    **kwargs
) -> dict:
    """
    获取累积式fold信息统计

    返回:
        包含fold数量、训练集增长信息等的字典
    """
    folds = list(cumulative_splits_by_days(
        D,
        train_days=train_days,
        inner_val_ratio=inner_val_ratio,
        **kwargs
    ))

    if not folds:
        return {"num_folds": 0}

    # 计算每个fold的统计信息
    fold_stats = []
    for i, (idx_train, idx_valid, idx_test) in enumerate(folds):
        fold_stats.append({
            "fold": i,
            "train_days": len(idx_train),
            "valid_days": len(idx_valid),
            "test_days": len(idx_test),
        })

    return {
        "num_folds": len(folds),
        "fold_stats": fold_stats,
        "final_train_coverage": len(folds[-1][0]) / D if folds else 0.0,
        "train_days_per_window": train_days,
        "inner_val_ratio": inner_val_ratio,
    }
